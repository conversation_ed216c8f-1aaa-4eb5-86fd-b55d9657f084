#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DXF结构化解析器 V4 - 基于图像版面识别
结合DXF解析和图像版面识别技术，实现更准确的CAD图纸结构化解析

主要特性：
1. DXF转PDF渲染
2. PaddleOCR版面识别
3. 版面区域与DXF实体匹配
4. 智能表格和图例识别
"""

import os
import json
import ezdxf
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from collections import defaultdict
from tqdm import tqdm
import cv2
import tempfile

# 图像处理和OCR相关
try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("警告: PyMuPDF未安装，无法进行DXF到PDF转换")

try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False
    print("警告: PaddleOCR未安装，无法进行版面识别")

# 导入v3版本的基础功能
try:
    from .dxf_parser_structured_v3 import DXFComprehensiveParser as DXFParserV3
except ImportError:
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from dxf_parser_structured_v3 import DXFComprehensiveParser as DXFParserV3


class DXFLayoutAnalyzer:
    """DXF版面分析器 - 基于图像识别"""
    
    def __init__(self):
        self.ocr = None
        if PADDLEOCR_AVAILABLE:
            # 初始化PaddleOCR，启用版面分析
            try:
                self.ocr = PaddleOCR(
                    use_angle_cls=True,
                    lang='ch'
                )
            except Exception as e:
                print(f"PaddleOCR初始化失败: {e}")
                self.ocr = None
    
    def dxf_to_pdf(self, dxf_path: str, output_pdf: str = None, use_advanced: bool = True) -> str:
        """将DXF文件转换为PDF - 支持多种渲染方法"""
        try:
            # 创建输出PDF路径
            if output_pdf is None:
                output_pdf = dxf_path.replace('.dxf', '_layout.pdf')

            # 尝试使用高级渲染器
            if use_advanced:
                try:
                    from .advanced_dxf_renderer import AdvancedDXFRenderer
                    renderer = AdvancedDXFRenderer()
                    result = renderer.render_dxf_to_pdf(dxf_path, output_pdf)
                    if result:
                        print(f"✅ 使用高级渲染器成功")
                        return result
                    else:
                        print("⚠️  高级渲染器失败，回退到基础渲染")
                except ImportError:
                    print("⚠️  高级渲染器不可用，使用基础渲染")
                except Exception as e:
                    print(f"⚠️  高级渲染器异常: {e}，回退到基础渲染")

            # 回退到基础渲染
            doc = ezdxf.readfile(dxf_path)
            self._render_dxf_to_pdf(doc, output_pdf)

            if os.path.exists(output_pdf):
                print(f"✅ 使用基础渲染器成功")
                return output_pdf
            else:
                print(f"❌ 基础渲染器也失败了")
                return None

        except Exception as e:
            print(f"DXF转PDF失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _render_dxf_to_pdf(self, doc, output_pdf: str):
        """使用matplotlib渲染DXF到PDF - 优化版本"""
        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_pdf import PdfPages
            import matplotlib.patches as patches
            from matplotlib.patches import Circle, Arc, Polygon
            import matplotlib.colors as mcolors

            # 获取模型空间
            msp = doc.modelspace()

            # 计算图纸边界 - 更精确的边界计算
            bounds = self._calculate_drawing_bounds(msp)
            if not bounds:
                print("警告: 无法计算图纸边界，使用默认边界")
                bounds = {'min_x': 0, 'min_y': 0, 'max_x': 1000, 'max_y': 1000}

            # 计算合适的图纸尺寸
            width = bounds['max_x'] - bounds['min_x']
            height = bounds['max_y'] - bounds['min_y']

            # 设置合适的图形尺寸（保持比例）
            if width > height:
                fig_width = 20
                fig_height = 20 * height / width
            else:
                fig_height = 20
                fig_width = 20 * width / height

            # 创建PDF
            with PdfPages(output_pdf) as pdf:
                fig, ax = plt.subplots(figsize=(fig_width, fig_height))

                # 设置白色背景
                fig.patch.set_facecolor('white')
                ax.set_facecolor('white')

                # 统计实体类型
                entity_count = {}

                # 绘制实体 - 支持更多实体类型
                for entity in msp:
                    entity_type = entity.dxftype()
                    entity_count[entity_type] = entity_count.get(entity_type, 0) + 1

                    try:
                        self._render_entity(ax, entity)
                    except Exception as e:
                        print(f"渲染实体失败 {entity_type}: {e}")
                        continue

                # 设置坐标轴和显示
                margin = max(width, height) * 0.05  # 5%边距
                ax.set_xlim(bounds['min_x'] - margin, bounds['max_x'] + margin)
                ax.set_ylim(bounds['min_y'] - margin, bounds['max_y'] + margin)
                ax.set_aspect('equal')

                # 移除坐标轴和网格，让图纸更清晰
                ax.set_xticks([])
                ax.set_yticks([])
                ax.spines['top'].set_visible(False)
                ax.spines['right'].set_visible(False)
                ax.spines['bottom'].set_visible(False)
                ax.spines['left'].set_visible(False)

                # 保存PDF
                pdf.savefig(fig, bbox_inches='tight', dpi=300,
                           facecolor='white', edgecolor='none')
                plt.close(fig)

                print(f"✅ PDF渲染完成，实体统计: {entity_count}")

        except Exception as e:
            print(f"渲染PDF失败: {e}")
            import traceback
            traceback.print_exc()
            # 备用方案：创建简单的PDF
            self._create_simple_pdf(output_pdf)
    
    def _calculate_drawing_bounds(self, msp):
        """计算图纸的精确边界"""
        min_x, min_y = float('inf'), float('inf')
        max_x, max_y = float('-inf'), float('-inf')

        found_entities = False

        for entity in msp:
            try:
                entity_type = entity.dxftype()
                bounds = None

                if entity_type == 'LINE':
                    start, end = entity.dxf.start, entity.dxf.end
                    bounds = [start.x, start.y, end.x, end.y]

                elif entity_type in ['TEXT', 'MTEXT']:
                    insert = entity.dxf.insert
                    # 估算文本边界
                    text_height = getattr(entity.dxf, 'height', 10)
                    bounds = [insert.x, insert.y, insert.x + text_height * 5, insert.y + text_height]

                elif entity_type == 'LWPOLYLINE':
                    points = list(entity.get_points())
                    if points:
                        xs, ys = zip(*[(p[0], p[1]) for p in points])
                        bounds = [min(xs), min(ys), max(xs), max(ys)]

                elif entity_type == 'CIRCLE':
                    center = entity.dxf.center
                    radius = entity.dxf.radius
                    bounds = [center.x - radius, center.y - radius,
                             center.x + radius, center.y + radius]

                elif entity_type == 'ARC':
                    center = entity.dxf.center
                    radius = entity.dxf.radius
                    bounds = [center.x - radius, center.y - radius,
                             center.x + radius, center.y + radius]

                elif entity_type == 'INSERT':
                    insert = entity.dxf.insert
                    bounds = [insert.x, insert.y, insert.x + 50, insert.y + 50]  # 估算块大小

                if bounds:
                    min_x = min(min_x, bounds[0], bounds[2])
                    max_x = max(max_x, bounds[0], bounds[2])
                    min_y = min(min_y, bounds[1], bounds[3])
                    max_y = max(max_y, bounds[1], bounds[3])
                    found_entities = True

            except Exception as e:
                continue

        if not found_entities:
            return None

        return {
            'min_x': min_x,
            'min_y': min_y,
            'max_x': max_x,
            'max_y': max_y
        }

    def _render_entity(self, ax, entity):
        """渲染单个DXF实体"""
        entity_type = entity.dxftype()

        # 获取实体颜色和线宽
        color = self._get_entity_color(entity)
        linewidth = self._get_entity_linewidth(entity)

        if entity_type == 'LINE':
            start, end = entity.dxf.start, entity.dxf.end
            ax.plot([start.x, end.x], [start.y, end.y],
                   color=color, linewidth=linewidth)

        elif entity_type in ['TEXT', 'MTEXT']:
            insert = entity.dxf.insert
            text = getattr(entity.dxf, 'text', str(entity))
            height = getattr(entity.dxf, 'height', 10)
            rotation = getattr(entity.dxf, 'rotation', 0)

            ax.text(insert.x, insert.y, text,
                   fontsize=max(4, height/2), color=color,
                   rotation=rotation, ha='left', va='bottom')

        elif entity_type == 'LWPOLYLINE':
            points = list(entity.get_points())
            if len(points) > 1:
                xs, ys = zip(*[(p[0], p[1]) for p in points])
                if entity.closed:
                    xs = xs + (xs[0],)
                    ys = ys + (ys[0],)
                ax.plot(xs, ys, color=color, linewidth=linewidth)

        elif entity_type == 'CIRCLE':
            center = entity.dxf.center
            radius = entity.dxf.radius
            circle = Circle((center.x, center.y), radius,
                          fill=False, color=color, linewidth=linewidth)
            ax.add_patch(circle)

        elif entity_type == 'ARC':
            center = entity.dxf.center
            radius = entity.dxf.radius
            start_angle = entity.dxf.start_angle
            end_angle = entity.dxf.end_angle

            # 转换角度为度数
            start_deg = np.degrees(start_angle)
            end_deg = np.degrees(end_angle)

            arc = Arc((center.x, center.y), 2*radius, 2*radius,
                     theta1=start_deg, theta2=end_deg,
                     color=color, linewidth=linewidth)
            ax.add_patch(arc)

        elif entity_type == 'SPLINE':
            # 简化处理样条曲线
            try:
                points = entity.control_points
                if points and len(points) > 1:
                    xs, ys = zip(*[(p.x, p.y) for p in points])
                    ax.plot(xs, ys, color=color, linewidth=linewidth, linestyle='--')
            except:
                pass

        elif entity_type == 'HATCH':
            # 简化处理填充
            try:
                # 获取填充边界
                boundary_paths = entity.paths
                for path in boundary_paths:
                    if hasattr(path, 'edges'):
                        for edge in path.edges:
                            if hasattr(edge, 'start') and hasattr(edge, 'end'):
                                ax.plot([edge.start.x, edge.end.x],
                                       [edge.start.y, edge.end.y],
                                       color=color, linewidth=linewidth*0.5, alpha=0.7)
            except:
                pass

    def _get_entity_color(self, entity):
        """获取实体颜色"""
        try:
            color_index = entity.dxf.color
            if color_index == 256:  # BYLAYER
                return 'black'
            elif color_index == 0:  # BYBLOCK
                return 'black'
            else:
                # 简化的颜色映射
                color_map = {
                    1: 'red', 2: 'yellow', 3: 'green', 4: 'cyan',
                    5: 'blue', 6: 'magenta', 7: 'white'
                }
                return color_map.get(color_index, 'black')
        except:
            return 'black'

    def _get_entity_linewidth(self, entity):
        """获取实体线宽"""
        try:
            lineweight = getattr(entity.dxf, 'lineweight', -1)
            if lineweight > 0:
                return max(0.1, lineweight / 100)  # 转换为matplotlib线宽
            else:
                return 0.5  # 默认线宽
        except:
            return 0.5

    def _create_simple_pdf(self, output_pdf: str):
        """创建简单的PDF作为备用方案"""
        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_pdf import PdfPages

            with PdfPages(output_pdf) as pdf:
                fig, ax = plt.subplots(figsize=(16, 12))
                ax.text(0.5, 0.5, 'DXF Layout Analysis\n(渲染失败，使用备用方案)',
                       ha='center', va='center', fontsize=20)
                pdf.savefig(fig)
                plt.close(fig)
        except:
            pass
    
    def pdf_to_image(self, pdf_path: str) -> List[np.ndarray]:
        """将PDF转换为图像"""
        if not PYMUPDF_AVAILABLE:
            raise ImportError("需要安装PyMuPDF: pip install PyMuPDF")
        
        images = []
        try:
            doc = fitz.open(pdf_path)
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                # 设置较高的分辨率以便OCR识别
                mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                
                # 转换为OpenCV格式
                nparr = np.frombuffer(img_data, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                images.append(img)
            
            doc.close()
            return images
            
        except Exception as e:
            print(f"PDF转图像失败: {e}")
            return []
    
    def analyze_layout(self, image: np.ndarray) -> Dict:
        """基于图像分析的版面识别 - 专注于布局而非文本"""
        try:
            print("🔍 开始版面布局分析...")

            # 1. 检测双页布局
            pages = self._detect_dual_page_layout(image)
            print(f"检测到 {len(pages)} 个页面")

            layout_result = {
                "pages": [],
                "total_pages": len(pages),
                "analysis_method": "基于图像的版面分析"
            }

            # 2. 分析每个页面的布局
            for i, page_region in enumerate(pages):
                print(f"分析页面 {i+1}...")
                page_analysis = self._analyze_single_page_layout(image, page_region, i+1)
                layout_result["pages"].append(page_analysis)

            return layout_result

        except Exception as e:
            print(f"版面分析失败: {e}")
            import traceback
            traceback.print_exc()
            return {"pages": [], "total_pages": 0, "analysis_method": "失败"}

    def _detect_dual_page_layout(self, image: np.ndarray) -> List[Dict]:
        """检测双页布局"""
        height, width = image.shape[:2]

        # 转换为灰度图进行分析
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # 检测页面边界（通过边缘检测）
        edges = cv2.Canny(gray, 50, 150)

        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 过滤出可能的页面边界
        page_candidates = []
        min_area = width * height * 0.2  # 页面至少占总面积的20%

        for contour in contours:
            area = cv2.contourArea(contour)
            if area > min_area:
                x, y, w, h = cv2.boundingRect(contour)
                # 页面应该有合理的宽高比
                aspect_ratio = w / h
                if 0.5 < aspect_ratio < 2.0:
                    page_candidates.append({
                        "x": x, "y": y, "width": w, "height": h,
                        "area": area, "aspect_ratio": aspect_ratio
                    })

        # 如果没有检测到明显的页面边界，按中线分割
        if len(page_candidates) < 2:
            print("未检测到明显页面边界，使用中线分割")
            mid_x = width // 2
            pages = [
                {"x": 0, "y": 0, "width": mid_x, "height": height},
                {"x": mid_x, "y": 0, "width": mid_x, "height": height}
            ]
        else:
            # 选择最大的两个区域作为页面
            page_candidates.sort(key=lambda x: x["area"], reverse=True)
            pages = page_candidates[:2]

            # 按x坐标排序（左页在前）
            pages.sort(key=lambda x: x["x"])

        return pages

    def _analyze_single_page_layout(self, image: np.ndarray, page_region: Dict, page_num: int) -> Dict:
        """分析单个页面的布局结构"""
        # 提取页面区域
        x, y, w, h = page_region["x"], page_region["y"], page_region["width"], page_region["height"]
        page_image = image[y:y+h, x:x+w]

        # 转换为灰度图
        if len(page_image.shape) == 3:
            gray = cv2.cvtColor(page_image, cv2.COLOR_BGR2GRAY)
        else:
            gray = page_image.copy()

        # 检测表格区域（通过线条检测）
        tables = self._detect_table_regions(gray, page_region)

        # 检测主图区域（排除表格后的主要区域）
        main_drawing = self._detect_main_drawing_region(gray, page_region, tables)

        # 检测图例区域
        legends = self._detect_legend_regions(gray, page_region, tables)

        page_analysis = {
            "page_number": page_num,
            "page_bounds": page_region,
            "main_drawing": main_drawing,
            "tables": tables,
            "legends": legends,
            "layout_summary": {
                "has_main_drawing": main_drawing is not None,
                "table_count": len(tables),
                "legend_count": len(legends)
            }
        }

        print(f"页面{page_num}: 主图={'有' if main_drawing else '无'}, 表格{len(tables)}个, 图例{len(legends)}个")

        return page_analysis

    def _detect_table_regions(self, gray_image: np.ndarray, page_region: Dict) -> List[Dict]:
        """检测表格区域 - 改进版本"""
        height, width = gray_image.shape

        # 方法1: 基于密集矩形区域检测
        tables_method1 = self._detect_tables_by_rectangles(gray_image, page_region)

        # 方法2: 基于文本密度检测
        tables_method2 = self._detect_tables_by_text_density(gray_image, page_region)

        # 方法3: 基于固定位置检测（根据工程图纸的常见布局）
        tables_method3 = self._detect_tables_by_position(gray_image, page_region)

        # 合并所有方法的结果
        all_tables = tables_method1 + tables_method2 + tables_method3

        # 去重和筛选
        filtered_tables = self._filter_and_merge_tables(all_tables)

        return filtered_tables

    def _detect_tables_by_rectangles(self, gray_image: np.ndarray, page_region: Dict) -> List[Dict]:
        """基于矩形结构检测表格"""
        height, width = gray_image.shape

        # 使用更小的核来检测细线条
        kernel_horizontal = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 1))
        kernel_vertical = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 15))

        # 检测水平和垂直线条
        horizontal_lines = cv2.morphologyEx(gray_image, cv2.MORPH_OPEN, kernel_horizontal)
        vertical_lines = cv2.morphologyEx(gray_image, cv2.MORPH_OPEN, kernel_vertical)

        # 合并线条
        table_mask = cv2.addWeighted(horizontal_lines, 0.5, vertical_lines, 0.5, 0.0)

        # 查找轮廓
        contours, _ = cv2.findContours(table_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        tables = []
        min_table_area = width * height * 0.005  # 表格至少占页面0.5%的面积
        max_table_area = width * height * 0.3    # 表格最多占页面30%的面积

        for contour in contours:
            area = cv2.contourArea(contour)
            if min_table_area < area < max_table_area:  # 限制表格大小范围
                x, y, w, h = cv2.boundingRect(contour)

                # 表格应该有合理的宽高比和位置
                aspect_ratio = w / h if h > 0 else 0
                if 0.8 < aspect_ratio < 5:  # 表格宽高比应该合理
                    # 表格通常在页面下半部分
                    if y > height * 0.5:
                        table_region = {
                            "x": page_region["x"] + x,
                            "y": page_region["y"] + y,
                            "width": w,
                            "height": h,
                            "area": area,
                            "position": self._classify_table_position(x, y, width, height),
                            "detection_method": "rectangles",
                            "aspect_ratio": aspect_ratio
                        }
                        tables.append(table_region)

        return tables

    def _detect_tables_by_text_density(self, gray_image: np.ndarray, page_region: Dict) -> List[Dict]:
        """基于文本密度检测表格"""
        # 这个方法需要结合DXF文本实体信息
        # 暂时返回空列表，可以后续完善
        return []

    def _detect_tables_by_position(self, gray_image: np.ndarray, page_region: Dict) -> List[Dict]:
        """基于固定位置检测表格（工程图纸常见布局）"""
        height, width = gray_image.shape
        tables = []

        # 根据工程图纸的常见布局，表格通常在右下角和左下角
        # 定义可能的表格区域 - 更精确的位置和尺寸
        table_regions = [
            # 右下角表格 - 通常在页面的右下角
            {
                "x": int(width * 0.65),
                "y": int(height * 0.7),
                "width": int(width * 0.3),
                "height": int(height * 0.25),
                "position": "右下角"
            },
            # 左下角表格 - 通常在页面的左下角
            {
                "x": int(width * 0.05),
                "y": int(height * 0.7),
                "width": int(width * 0.3),
                "height": int(height * 0.25),
                "position": "左下角"
            },
            # 中下方表格 - 有时表格在页面中下方
            {
                "x": int(width * 0.35),
                "y": int(height * 0.75),
                "width": int(width * 0.3),
                "height": int(height * 0.2),
                "position": "中下角"
            }
        ]

        for region in table_regions:
            # 检查该区域是否有足够的内容
            roi = gray_image[region["y"]:region["y"]+region["height"],
                           region["x"]:region["x"]+region["width"]]

            if roi.size > 0:
                # 计算该区域的内容密度
                non_white_pixels = np.sum(roi < 240)  # 非白色像素
                total_pixels = roi.size
                density = non_white_pixels / total_pixels

                # 检测该区域是否有表格特征（线条结构）
                has_table_structure = self._check_table_structure(roi)

                # 如果密度足够高且有表格结构，认为是表格区域
                if density > 0.05 and has_table_structure:  # 降低密度要求但增加结构检查
                    table_region = {
                        "x": page_region["x"] + region["x"],
                        "y": page_region["y"] + region["y"],
                        "width": region["width"],
                        "height": region["height"],
                        "area": region["width"] * region["height"],
                        "position": region["position"],
                        "detection_method": "position",
                        "content_density": density,
                        "has_structure": has_table_structure
                    }
                    tables.append(table_region)

        return tables

    def _check_table_structure(self, roi: np.ndarray) -> bool:
        """检查区域是否有表格结构特征"""
        try:
            # 使用小核检测水平和垂直线条
            kernel_h = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 1))
            kernel_v = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 15))

            horizontal = cv2.morphologyEx(roi, cv2.MORPH_OPEN, kernel_h)
            vertical = cv2.morphologyEx(roi, cv2.MORPH_OPEN, kernel_v)

            # 计算线条密度
            h_density = np.sum(horizontal > 0) / horizontal.size
            v_density = np.sum(vertical > 0) / vertical.size

            # 如果水平和垂直线条密度都足够，认为有表格结构
            return h_density > 0.01 and v_density > 0.01

        except Exception:
            return False

    def _filter_and_merge_tables(self, tables: List[Dict]) -> List[Dict]:
        """过滤和合并表格检测结果"""
        if not tables:
            return []

        # 按面积排序
        tables.sort(key=lambda t: t["area"], reverse=True)

        # 去重：如果两个表格重叠度很高，保留面积更大的
        filtered_tables = []
        for table in tables:
            is_duplicate = False
            for existing in filtered_tables:
                if self._calculate_overlap_ratio(table, existing) > 0.5:
                    is_duplicate = True
                    break

            if not is_duplicate:
                filtered_tables.append(table)

        # 最多返回2个表格（左下和右下）
        return filtered_tables[:2]

    def _calculate_overlap_ratio(self, table1: Dict, table2: Dict) -> float:
        """计算两个表格的重叠比例"""
        x1_min, y1_min = table1["x"], table1["y"]
        x1_max, y1_max = x1_min + table1["width"], y1_min + table1["height"]

        x2_min, y2_min = table2["x"], table2["y"]
        x2_max, y2_max = x2_min + table2["width"], y2_min + table2["height"]

        # 计算重叠区域
        overlap_x_min = max(x1_min, x2_min)
        overlap_y_min = max(y1_min, y2_min)
        overlap_x_max = min(x1_max, x2_max)
        overlap_y_max = min(y1_max, y2_max)

        if overlap_x_max <= overlap_x_min or overlap_y_max <= overlap_y_min:
            return 0.0

        overlap_area = (overlap_x_max - overlap_x_min) * (overlap_y_max - overlap_y_min)
        table1_area = table1["width"] * table1["height"]
        table2_area = table2["width"] * table2["height"]

        return overlap_area / min(table1_area, table2_area)

    def _classify_table_position(self, x: int, y: int, page_width: int, page_height: int) -> str:
        """分类表格位置"""
        mid_x = page_width // 2

        if x < mid_x:
            return "左下角"
        else:
            return "右下角"

    def _detect_main_drawing_region(self, gray_image: np.ndarray, page_region: Dict, tables: List[Dict]) -> Dict:
        """检测主图区域"""
        height, width = gray_image.shape

        # 主图通常占据页面的中央大部分区域
        # 排除表格区域后的剩余主要区域

        # 计算非表格区域
        main_region_y_start = int(height * 0.1)  # 从页面10%开始
        main_region_y_end = int(height * 0.7)    # 到页面70%结束（避开下方表格）

        main_drawing = {
            "x": page_region["x"] + int(width * 0.05),
            "y": page_region["y"] + main_region_y_start,
            "width": int(width * 0.9),
            "height": main_region_y_end - main_region_y_start,
            "position": "居中主图区域"
        }

        return main_drawing

    def _detect_legend_regions(self, gray_image: np.ndarray, page_region: Dict, tables: List[Dict]) -> List[Dict]:
        """检测图例区域"""
        # 图例通常在表格附近或页面边缘
        # 这里简化处理，如果有特殊的图例检测需求可以进一步优化

        legends = []
        height, width = gray_image.shape

        # 检查是否有明显的图例区域（通常包含符号和说明文字）
        # 暂时返回空列表，可以根据具体需求添加图例检测逻辑

        return legends

    def _optimize_image_for_ocr(self, image: np.ndarray) -> np.ndarray:
        """优化图像以提高OCR识别率"""
        try:
            # 如果图像太大，进行缩放
            height, width = image.shape[:2]
            max_size = 4000  # PaddleOCR的建议最大尺寸

            if max(height, width) > max_size:
                if height > width:
                    new_height = max_size
                    new_width = int(width * max_size / height)
                else:
                    new_width = max_size
                    new_height = int(height * max_size / width)

                image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
                print(f"图像已缩放至: {new_width}x{new_height}")

            return image

        except Exception as e:
            print(f"图像优化失败: {e}")
            return image
    
    def detect_layout_structures(self, layout_result: Dict) -> Dict:
        """检测版面结构（表格、图例等）"""
        regions = layout_result.get("regions", [])
        
        # 分析文本分布，识别表格和图例
        tables = self._detect_tables_from_layout(regions)
        legends = self._detect_legends_from_layout(regions)
        drawings = self._detect_drawing_areas(regions, layout_result.get("image_shape"))
        
        return {
            "tables": tables,
            "legends": legends,
            "drawings": drawings,
            "all_regions": regions
        }
    
    def _detect_tables_from_layout(self, regions: List[Dict]) -> List[Dict]:
        """从版面中检测表格"""
        tables = []
        
        # 按Y坐标分组，寻找网格状分布
        y_groups = defaultdict(list)
        for region in regions:
            y_center = (region["bbox"]["y_min"] + region["bbox"]["y_max"]) / 2
            y_key = round(y_center / 50) * 50  # 50像素为一组
            y_groups[y_key].append(region)
        
        # 寻找连续的行组成表格
        sorted_y_keys = sorted(y_groups.keys())
        current_table = []
        
        for i, y_key in enumerate(sorted_y_keys):
            row_regions = y_groups[y_key]
            
            # 如果这一行有多个文本区域，可能是表格行
            if len(row_regions) >= 2:
                # 按X坐标排序
                row_regions.sort(key=lambda r: r["bbox"]["x_min"])
                current_table.append({
                    "y_position": y_key,
                    "regions": row_regions,
                    "column_count": len(row_regions)
                })
            else:
                # 如果当前表格有足够的行，保存它
                if len(current_table) >= 3:
                    tables.append({
                        "type": "table",
                        "rows": current_table,
                        "row_count": len(current_table),
                        "bbox": self._calculate_table_bbox(current_table)
                    })
                current_table = []
        
        # 处理最后一个表格
        if len(current_table) >= 3:
            tables.append({
                "type": "table",
                "rows": current_table,
                "row_count": len(current_table),
                "bbox": self._calculate_table_bbox(current_table)
            })
        
        return tables
    
    def _detect_legends_from_layout(self, regions: List[Dict]) -> List[Dict]:
        """从版面中检测图例"""
        legends = []
        
        # 寻找包含图例关键词的区域
        legend_keywords = ["图例", "说明", "符号", "标识", "LEGEND"]
        
        for region in regions:
            text = region.get("text", "")
            if any(keyword in text for keyword in legend_keywords):
                legends.append({
                    "type": "legend",
                    "bbox": region["bbox"],
                    "text": text,
                    "confidence": region.get("confidence", 0)
                })
        
        return legends
    
    def _detect_drawing_areas(self, regions: List[Dict], image_shape) -> List[Dict]:
        """检测绘图区域"""
        if not image_shape:
            return []
        
        height, width = image_shape[:2]
        
        # 计算文本覆盖的区域
        text_areas = []
        for region in regions:
            bbox = region["bbox"]
            text_areas.append([
                bbox["x_min"], bbox["y_min"],
                bbox["x_max"], bbox["y_max"]
            ])
        
        # 寻找大片空白区域作为绘图区域
        # 这里简化处理，将非文本区域视为绘图区域
        drawing_areas = [{
            "type": "drawing",
            "bbox": {
                "x_min": 0,
                "y_min": 0,
                "x_max": width,
                "y_max": height
            },
            "description": "主绘图区域"
        }]
        
        return drawing_areas
    
    def _calculate_table_bbox(self, table_rows: List[Dict]) -> Dict:
        """计算表格的边界框"""
        all_regions = []
        for row in table_rows:
            all_regions.extend(row["regions"])
        
        if not all_regions:
            return {"x_min": 0, "y_min": 0, "x_max": 0, "y_max": 0}
        
        x_min = min(r["bbox"]["x_min"] for r in all_regions)
        y_min = min(r["bbox"]["y_min"] for r in all_regions)
        x_max = max(r["bbox"]["x_max"] for r in all_regions)
        y_max = max(r["bbox"]["y_max"] for r in all_regions)
        
        return {
            "x_min": x_min,
            "y_min": y_min,
            "x_max": x_max,
            "y_max": y_max
        }


class DXFStructuredParserV4(DXFParserV3):
    """DXF结构化解析器 V4 - 集成图像版面识别"""
    
    def __init__(self, dxf_path: str, include_coordinates: bool = True, include_raw_data: bool = True):
        super().__init__(dxf_path, include_coordinates, include_raw_data)
        self.layout_analyzer = DXFLayoutAnalyzer()
        self.layout_results = None
        self.pdf_path = None
    
    def analyze_layout_with_ocr(self) -> Dict:
        """使用OCR进行版面分析"""
        try:
            # 1. DXF转PDF
            print("🔄 转换DXF到PDF...")
            self.pdf_path = self.layout_analyzer.dxf_to_pdf(self.dxf_path)
            if not self.pdf_path:
                print("❌ DXF转PDF失败，使用传统解析方法")
                return self.generate_comprehensive_output()
            
            # 2. PDF转图像
            print("🔄 转换PDF到图像...")
            images = self.layout_analyzer.pdf_to_image(self.pdf_path)
            if not images:
                print("❌ PDF转图像失败，使用传统解析方法")
                return self.generate_comprehensive_output()
            
            # 3. 版面布局分析
            print("🔄 进行版面布局分析...")
            layout_results = []
            for i, image in enumerate(images):
                print(f"  分析第{i+1}页...")
                layout_result = self.layout_analyzer.analyze_layout(image)
                layout_results.append({
                    "page": i + 1,
                    "layout": layout_result
                })
            
            self.layout_results = layout_results
            
            # 4. 结合DXF解析和版面分析
            print("🔄 匹配DXF实体与版面结构...")
            return self.generate_layout_aware_output()
            
        except Exception as e:
            print(f"❌ 版面分析失败: {e}")
            print("🔄 回退到传统解析方法...")
            return self.generate_comprehensive_output()
    
    def generate_layout_aware_output(self) -> Dict:
        """生成基于版面识别的结构化输出"""
        # 先进行传统DXF解析
        traditional_result = self.generate_comprehensive_output()
        
        if not self.layout_results:
            return traditional_result
        
        # 基于版面识别重构图纸结构
        enhanced_result = self._integrate_layout_with_dxf_analysis(traditional_result)

        return enhanced_result

    def _integrate_layout_with_dxf_analysis(self, traditional_result: Dict) -> Dict:
        """将版面识别结果与DXF解析结果整合"""
        print("🔄 整合版面识别与DXF解析结果...")

        # 建立坐标映射关系
        self._establish_coordinate_mapping(traditional_result)

        # 创建新的结构化结果
        integrated_result = {
            "文件元数据": traditional_result.get("文件元数据", {}),
            "版面分析": {
                "分析方法": "基于图像的版面布局分析",
                "总页面数": 0,
                "布局详情": []
            },
            "图纸结构": {
                "图纸数量": 0,
                "图纸列表": []
            },
            "文本内容": traditional_result.get("文本内容", {}),
            "表格数据": [],
            "实体匹配": traditional_result.get("实体匹配", {}),
            "坐标映射信息": getattr(self, 'coordinate_mapping', {})
        }

        # 处理版面识别结果
        total_pages = 0
        for page_result in self.layout_results:
            layout = page_result["layout"]
            if "pages" in layout:
                total_pages += layout["total_pages"]

                for page in layout["pages"]:
                    # 版面分析详情
                    page_info = {
                        "PDF页面": page_result["page"],
                        "图纸页面": page["page_number"],
                        "主图区域": page.get("main_drawing"),
                        "表格区域": page.get("tables", []),
                        "图例区域": page.get("legends", []),
                        "布局摘要": page.get("layout_summary", {}),
                        "检测到的表格": len(page.get("tables", [])),
                        "检测到的图例": len(page.get("legends", [])),
                        "检测到的绘图区域": 1 if page.get("main_drawing") else 0
                    }
                    integrated_result["版面分析"]["布局详情"].append(page_info)

                    # 创建对应的图纸结构
                    drawing_structure = self._create_drawing_structure_from_layout(page, traditional_result)
                    integrated_result["图纸结构"]["图纸列表"].append(drawing_structure)

                    # 为每个表格创建表格数据
                    for table in page.get("tables", []):
                        table_data = self._create_table_data_from_layout(table, traditional_result, page["page_number"])
                        integrated_result["表格数据"].append(table_data)

        integrated_result["版面分析"]["总页面数"] = total_pages
        integrated_result["图纸结构"]["图纸数量"] = total_pages

        print(f"✅ 整合完成: {total_pages}个图纸页面, {len(integrated_result['表格数据'])}个表格")

        return integrated_result

    def _establish_coordinate_mapping(self, traditional_result: Dict):
        """建立图像坐标与DXF坐标的映射关系"""
        try:
            # 获取DXF坐标范围
            all_texts = traditional_result.get("文本内容", {}).get("所有文本", [])
            if not all_texts:
                print("⚠️  没有文本数据，无法建立坐标映射")
                return

            # 计算DXF坐标范围
            dxf_x_coords = [text.get("x", 0) for text in all_texts if text.get("x") is not None]
            dxf_y_coords = [text.get("y", 0) for text in all_texts if text.get("y") is not None]

            if not dxf_x_coords or not dxf_y_coords:
                print("⚠️  DXF坐标数据不完整")
                return

            dxf_bounds = {
                "x_min": min(dxf_x_coords),
                "x_max": max(dxf_x_coords),
                "y_min": min(dxf_y_coords),
                "y_max": max(dxf_y_coords)
            }

            # 获取图像坐标范围（从版面识别结果）
            if hasattr(self, 'layout_results') and self.layout_results:
                for page_result in self.layout_results:
                    layout = page_result["layout"]
                    if "pages" in layout and layout["pages"]:
                        # 假设图像总尺寸（从第一个页面推算）
                        first_page = layout["pages"][0]
                        page_bounds = first_page.get("page_bounds", {})

                        # 图像坐标范围（像素）
                        image_bounds = {
                            "x_min": 0,
                            "x_max": page_bounds.get("width", 1000) * 2,  # 双页
                            "y_min": 0,
                            "y_max": page_bounds.get("height", 1000)
                        }

                        # 建立映射关系
                        self.coordinate_mapping = {
                            "dxf_bounds": dxf_bounds,
                            "image_bounds": image_bounds,
                            "scale_x": (dxf_bounds["x_max"] - dxf_bounds["x_min"]) / (image_bounds["x_max"] - image_bounds["x_min"]),
                            "scale_y": (dxf_bounds["y_max"] - dxf_bounds["y_min"]) / (image_bounds["y_max"] - image_bounds["y_min"]),
                            "offset_x": dxf_bounds["x_min"],
                            "offset_y": dxf_bounds["y_min"]
                        }

                        print(f"✅ 坐标映射建立成功:")
                        print(f"   DXF范围: X({dxf_bounds['x_min']:.0f} ~ {dxf_bounds['x_max']:.0f}), Y({dxf_bounds['y_min']:.0f} ~ {dxf_bounds['y_max']:.0f})")
                        print(f"   图像范围: X({image_bounds['x_min']} ~ {image_bounds['x_max']}), Y({image_bounds['y_min']} ~ {image_bounds['y_max']})")
                        print(f"   缩放比例: X({self.coordinate_mapping['scale_x']:.0f}), Y({self.coordinate_mapping['scale_y']:.0f})")
                        break

        except Exception as e:
            print(f"⚠️  建立坐标映射失败: {e}")
            self.coordinate_mapping = {}

    def _create_drawing_structure_from_layout(self, page_layout: Dict, traditional_result: Dict) -> Dict:
        """基于版面布局创建图纸结构并填充实际数据"""
        page_num = page_layout["page_number"]
        main_drawing = page_layout.get("main_drawing")
        tables = page_layout.get("tables", [])
        page_bounds = page_layout.get("page_bounds", {})

        # 获取DXF实体数据
        all_texts = traditional_result.get("文本内容", {}).get("所有文本", [])
        all_entities = self._get_all_entities_from_dxf()

        # 根据页面边界筛选实体
        page_entities = self._filter_entities_by_bounds(all_entities, page_bounds)
        page_texts = self._filter_texts_by_bounds(all_texts, page_bounds)

        # 分配实体到主图和表格区域
        main_drawing_entities = self._filter_entities_by_bounds(page_entities, main_drawing) if main_drawing else []
        main_drawing_texts = self._filter_texts_by_bounds(page_texts, main_drawing) if main_drawing else []

        drawing_structure = {
            "图纸名称": f"图纸页面{page_num}",
            "区域": f"页面{page_num}区域",
            "页面边界": page_bounds,
            "文本数量": len(page_texts),
            "实体统计": self._count_entities_by_type(page_entities),
            "主图": {
                "区域类型": "主图内容",
                "区域边界": main_drawing if main_drawing else {},
                "描述": f"页面{page_num}的主要绘图区域",
                "实体数量": len(main_drawing_entities),
                "文本数量": len(main_drawing_texts),
                "实体统计": self._count_entities_by_type(main_drawing_entities),
                "主要文本": main_drawing_texts[:10] if main_drawing_texts else []  # 显示前10个文本
            },
            "表格区域": []
        }

        # 添加表格区域信息和数据
        for i, table in enumerate(tables):
            table_entities = self._filter_entities_by_bounds(page_entities, table)
            table_texts = self._filter_texts_by_bounds(page_texts, table)

            table_info = {
                "表格名称": f"表格{i+1}",
                "位置": table.get("position", "未知"),
                "区域边界": {
                    "x": table.get("x", 0),
                    "y": table.get("y", 0),
                    "width": table.get("width", 0),
                    "height": table.get("height", 0)
                },
                "检测方法": table.get("detection_method", "未知"),
                "实体数量": len(table_entities),
                "文本数量": len(table_texts),
                "实体统计": self._count_entities_by_type(table_entities),
                "表格文本": table_texts[:20] if table_texts else []  # 显示前20个文本
            }
            drawing_structure["表格区域"].append(table_info)

        return drawing_structure

    def _create_table_data_from_layout(self, table_layout: Dict, traditional_result: Dict, page_num: int) -> Dict:
        """基于表格布局创建表格数据并填充内容"""
        # 获取表格区域内的文本和实体
        all_texts = traditional_result.get("文本内容", {}).get("所有文本", [])
        all_entities = self._get_all_entities_from_dxf()

        # 筛选表格区域内的内容
        table_texts = self._filter_texts_by_bounds(all_texts, table_layout)
        table_entities = self._filter_entities_by_bounds(all_entities, table_layout)

        # 分析表格结构
        table_structure = self._analyze_table_structure(table_texts, table_layout)

        return {
            "表格名称": f"页面{page_num}_{table_layout.get('position', '未知位置')}",
            "位置": table_layout.get("position", "未知"),
            "所属页面": page_num,
            "区域边界": {
                "x": table_layout.get("x", 0),
                "y": table_layout.get("y", 0),
                "width": table_layout.get("width", 0),
                "height": table_layout.get("height", 0)
            },
            "面积": table_layout.get("area", 0),
            "检测方法": table_layout.get("detection_method", "未知"),
            "内容密度": table_layout.get("content_density", 0),
            "实体统计": self._count_entities_by_type(table_entities),
            "文本数量": len(table_texts),
            "行数": table_structure.get("estimated_rows", "待解析"),
            "列数": table_structure.get("estimated_cols", "待解析"),
            "表格文本": [text.get("text", "") for text in table_texts[:30]],  # 前30个文本
            "主要内容": self._extract_table_key_content(table_texts)
        }

    def _analyze_table_structure(self, table_texts: List[Dict], table_layout: Dict) -> Dict:
        """分析表格结构"""
        if not table_texts:
            return {"estimated_rows": 0, "estimated_cols": 0}

        # 简单的行列估算（基于文本位置）
        y_positions = [text.get("y", 0) for text in table_texts]
        x_positions = [text.get("x", 0) for text in table_texts]

        # 估算行数（基于Y坐标的唯一值）
        unique_y = len(set([round(y, -1) for y in y_positions]))  # 四舍五入到10
        estimated_rows = max(1, unique_y // 2)  # 粗略估算

        # 估算列数（基于X坐标的分布）
        unique_x = len(set([round(x, -2) for x in x_positions]))  # 四舍五入到100
        estimated_cols = max(1, min(unique_x, 10))  # 限制在合理范围内

        return {
            "estimated_rows": estimated_rows,
            "estimated_cols": estimated_cols,
            "text_distribution": {
                "y_range": [min(y_positions), max(y_positions)] if y_positions else [0, 0],
                "x_range": [min(x_positions), max(x_positions)] if x_positions else [0, 0]
            }
        }

    def _extract_table_key_content(self, table_texts: List[Dict]) -> List[str]:
        """提取表格关键内容"""
        if not table_texts:
            return []

        # 提取文本内容并过滤
        contents = []
        for text in table_texts:
            text_content = text.get("text", "").strip()
            if text_content and len(text_content) > 1:  # 过滤空文本和单字符
                contents.append(text_content)

        # 返回前20个有意义的内容
        return contents[:20]

    def _get_all_entities_from_dxf(self) -> List[Dict]:
        """从DXF文件获取所有实体"""
        entities = []
        try:
            doc = ezdxf.readfile(self.dxf_path)
            msp = doc.modelspace()

            for entity in msp:
                entity_info = {
                    "type": entity.dxftype(),
                    "layer": entity.dxf.layer,
                    "color": getattr(entity.dxf, 'color', 256),
                    "linetype": getattr(entity.dxf, 'linetype', 'BYLAYER')
                }

                # 获取实体的边界框
                try:
                    if hasattr(entity, 'dxf') and hasattr(entity.dxf, 'insert'):
                        # INSERT实体
                        entity_info["x"] = entity.dxf.insert.x
                        entity_info["y"] = entity.dxf.insert.y
                    elif hasattr(entity, 'dxf') and hasattr(entity.dxf, 'start'):
                        # LINE实体
                        entity_info["x"] = entity.dxf.start.x
                        entity_info["y"] = entity.dxf.start.y
                    elif hasattr(entity, 'dxf') and hasattr(entity.dxf, 'center'):
                        # CIRCLE实体
                        entity_info["x"] = entity.dxf.center.x
                        entity_info["y"] = entity.dxf.center.y
                    elif entity.dxftype() == 'TEXT':
                        # TEXT实体
                        entity_info["x"] = entity.dxf.insert.x
                        entity_info["y"] = entity.dxf.insert.y
                        entity_info["text"] = entity.dxf.text
                        entity_info["height"] = entity.dxf.height
                    elif entity.dxftype() == 'MTEXT':
                        # MTEXT实体
                        entity_info["x"] = entity.dxf.insert.x
                        entity_info["y"] = entity.dxf.insert.y
                        entity_info["text"] = entity.text
                    else:
                        # 其他实体，尝试获取边界框
                        try:
                            bbox = entity.bounding_box
                            if bbox:
                                entity_info["x"] = bbox.extmin.x
                                entity_info["y"] = bbox.extmin.y
                                entity_info["width"] = bbox.extmax.x - bbox.extmin.x
                                entity_info["height"] = bbox.extmax.y - bbox.extmin.y
                        except:
                            entity_info["x"] = 0
                            entity_info["y"] = 0
                except Exception as e:
                    entity_info["x"] = 0
                    entity_info["y"] = 0

                entities.append(entity_info)

        except Exception as e:
            print(f"获取DXF实体失败: {e}")

        return entities

    def _filter_entities_by_bounds(self, entities: List[Dict], bounds: Dict) -> List[Dict]:
        """根据边界框筛选实体（使用坐标映射）"""
        if not bounds or not entities:
            return []

        # 将图像坐标转换为DXF坐标
        dxf_bounds = self._image_to_dxf_bounds(bounds)
        if not dxf_bounds:
            return []

        filtered = []
        x_min = dxf_bounds["x_min"]
        y_min = dxf_bounds["y_min"]
        x_max = dxf_bounds["x_max"]
        y_max = dxf_bounds["y_max"]

        for entity in entities:
            entity_x = entity.get("x", 0)
            entity_y = entity.get("y", 0)

            # 检查实体是否在DXF坐标边界内
            if x_min <= entity_x <= x_max and y_min <= entity_y <= y_max:
                filtered.append(entity)

        return filtered

    def _filter_texts_by_bounds(self, texts: List[Dict], bounds: Dict) -> List[Dict]:
        """根据边界框筛选文本（使用坐标映射）"""
        if not bounds or not texts:
            return []

        # 将图像坐标转换为DXF坐标
        dxf_bounds = self._image_to_dxf_bounds(bounds)
        if not dxf_bounds:
            return []

        filtered = []
        x_min = dxf_bounds["x_min"]
        y_min = dxf_bounds["y_min"]
        x_max = dxf_bounds["x_max"]
        y_max = dxf_bounds["y_max"]

        for text in texts:
            text_x = text.get("x", 0)
            text_y = text.get("y", 0)

            # 检查文本是否在DXF坐标边界内
            if x_min <= text_x <= x_max and y_min <= text_y <= y_max:
                filtered.append(text)

        return filtered

    def _count_entities_by_type(self, entities: List[Dict]) -> Dict:
        """统计实体类型"""
        counts = {}
        for entity in entities:
            entity_type = entity.get("type", "UNKNOWN")
            counts[entity_type] = counts.get(entity_type, 0) + 1
        return counts

    def _image_to_dxf_bounds(self, image_bounds: Dict) -> Dict:
        """将图像坐标边界转换为DXF坐标边界"""
        if not hasattr(self, 'coordinate_mapping') or not self.coordinate_mapping:
            return None

        mapping = self.coordinate_mapping

        # 图像坐标
        img_x_min = image_bounds.get("x", 0)
        img_y_min = image_bounds.get("y", 0)
        img_x_max = img_x_min + image_bounds.get("width", 0)
        img_y_max = img_y_min + image_bounds.get("height", 0)

        # 转换为DXF坐标
        dxf_x_min = mapping["offset_x"] + img_x_min * mapping["scale_x"]
        dxf_y_min = mapping["offset_y"] + img_y_min * mapping["scale_y"]
        dxf_x_max = mapping["offset_x"] + img_x_max * mapping["scale_x"]
        dxf_y_max = mapping["offset_y"] + img_y_max * mapping["scale_y"]

        return {
            "x_min": dxf_x_min,
            "y_min": dxf_y_min,
            "x_max": dxf_x_max,
            "y_max": dxf_y_max
        }

    def extract_text_content(self) -> Dict:
        """提取DXF文件中的文本内容"""
        try:
            doc = ezdxf.readfile(self.dxf_path)
            msp = doc.modelspace()

            all_texts = []
            by_layer = {}

            for entity in msp:
                if entity.dxftype() in ['TEXT', 'MTEXT']:
                    text_info = {
                        "text": entity.text if entity.dxftype() == 'MTEXT' else entity.dxf.text,
                        "layer": entity.dxf.layer,
                        "x": entity.dxf.insert.x,
                        "y": entity.dxf.insert.y,
                        "height": getattr(entity.dxf, 'height', 0),
                        "type": entity.dxftype()
                    }

                    all_texts.append(text_info)

                    # 按图层分组
                    layer = entity.dxf.layer
                    if layer not in by_layer:
                        by_layer[layer] = []
                    by_layer[layer].append(text_info)

            return {
                "所有文本": all_texts,
                "按图层分组": by_layer,
                "文本统计": {
                    "总数": len(all_texts),
                    "图层数": len(by_layer)
                }
            }

        except Exception as e:
            print(f"提取文本内容失败: {e}")
            return {"所有文本": [], "按图层分组": {}, "文本统计": {"总数": 0, "图层数": 0}}

    def extract_table_data(self) -> List[Dict]:
        """提取表格数据（基础版本）"""
        # 这里返回空列表，实际的表格数据将通过版面识别填充
        return []
        
        # TODO: 实现更精确的DXF实体与版面区域匹配
        # 这里可以根据坐标匹配DXF实体到版面识别的区域
        
        return enhanced_result


def main():
    """主函数 - 测试V4版本"""
    import glob
    
    # 测试文件路径
    dxf_dir = "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test"
    output_dir = "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_v4"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 查找DXF文件
    dxf_files = glob.glob(os.path.join(dxf_dir, "*.dxf"))
    
    if not dxf_files:
        print(f"在 {dxf_dir} 中未找到DXF文件")
        return
    
    print(f"找到 {len(dxf_files)} 个DXF文件")
    print(f"输出目录: {output_dir}")
    
    # 解析配置
    include_coordinates = True
    include_raw_data = True
    print(f"解析配置: 坐标={include_coordinates}, 原始数据={include_raw_data}")
    
    success_count = 0
    
    # 处理每个文件
    for dxf_file in tqdm(dxf_files, desc="V4版面识别解析DXF文件"):
        try:
            # 创建解析器
            parser = DXFStructuredParserV4(
                dxf_file, 
                include_coordinates=include_coordinates,
                include_raw_data=include_raw_data
            )
            
            # 进行版面识别解析
            result = parser.analyze_layout_with_ocr()
            
            # 保存结果
            filename = os.path.basename(dxf_file).replace('.dxf', '.json')
            output_path = os.path.join(output_dir, filename)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"✓ 成功解析: {filename}")
            success_count += 1
            
        except Exception as e:
            print(f"✗ 解析失败: {os.path.basename(dxf_file)} - {e}")
    
    print(f"\n=== V4版面识别解析完成 ===")
    print(f"总文件数: {len(dxf_files)}")
    print(f"成功解析: {success_count}")
    print(f"解析失败: {len(dxf_files) - success_count}")
    print(f"解析成功率: {success_count/len(dxf_files)*100:.1f}%")


if __name__ == "__main__":
    main()
